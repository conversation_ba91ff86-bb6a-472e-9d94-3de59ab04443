;; =========================================================================
;; Excel CSV File Reader Script for Cadence Allegro
;; - Reads Excel CSV files into a table structure
;; - Handles CSV format with headers and multiple columns
;; - Returns structured table if file exists, nil if it doesn't
;; - Supports quoted fields, commas in data, and various CSV formats
;; =========================================================================

axlCmdRegister("read_csv_to_table" 'read_csv_to_table)

; Main function to read CSV file into a table
; Requires filename parameter
procedure(read_csv_to_table(filename)
  prog((csv_data)
    ; Display what we're doing
    printf("Reading CSV file: %s\n" filename)

    ; Check if file exists and read it
    csv_data = load_csv_file(filename)

    ; Display results
    if(csv_data then
      printf("Successfully loaded CSV data from: %s\n" filename)
      display_csv_table(csv_data)
    else
      printf("File not found or could not be read: %s\n" filename)
    )

    ; Return the table (or nil)
    return(csv_data)
  )
)

; Core function to load CSV file into table structure
procedure(load_csv_file(filename)
  prog((file_handle line csv_table headers row_data line_count row_count)
    ; Check if file exists
    unless(isFile(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )

    ; Try to open the file
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )

    ; Create main table to store CSV data
    csv_table = makeTable("csv_data" nil)
    csv_table["headers"] = nil
    csv_table["rows"] = nil
    csv_table["row_count"] = 0
    csv_table["column_count"] = 0

    ; Initialize counters
    line_count = 0
    row_count = 0
    headers = nil

    ; Read file line by line
    printf("Processing CSV file: %s\n" filename)
    while(gets(line file_handle)
      line_count = line_count + 1

      ; Remove trailing newline
      line = trim_string(line)

      ; Skip empty lines
      unless(strlen(line) == 0
        ; Parse CSV line
        row_data = parse_csv_line(line)

        if(line_count == 1 then
          ; First line contains headers
          headers = row_data
          csv_table["headers"] = headers
          csv_table["column_count"] = length(headers)
          printf("Headers found: %d columns\n" length(headers))
          foreach(header headers
            printf("  Column: %s\n" header)
          )
        else
          ; Data rows
          if(add_csv_row(csv_table row_data headers row_count) then
            row_count = row_count + 1
          )
        )
      )
    )

    ; Close the file
    close(file_handle)

    ; Update row count
    csv_table["row_count"] = row_count

    ; Report results
    printf("CSV processing complete:\n")
    printf("  Total lines: %d\n" line_count)
    printf("  Header columns: %d\n" csv_table["column_count"])
    printf("  Data rows: %d\n" row_count)

    ; Return the table if we have data, nil otherwise
    if(row_count > 0 then
      return(csv_table)
    else
      printf("No valid data rows found in CSV file\n")
      return(nil)
    )
  )
)

; Function to parse a CSV line into fields
procedure(parse_csv_line(line)
  prog((fields current_field in_quotes char i)
    fields = nil
    current_field = ""
    in_quotes = nil

    ; Process each character in the line
    for(i 1 strlen(line)
      char = substring(line i i)

      cond(
        ; Handle quotes
        (equal(char "\"")
          if(in_quotes then
            ; Check for escaped quote (double quote)
            if(i < strlen(line) && equal(substring(line (i+1) (i+1)) "\"") then
              ; Escaped quote - add single quote to field
              current_field = strcat(current_field "\"")
              i = i + 1  ; Skip next quote
            else
              ; End of quoted field
              in_quotes = nil
            )
          else
            ; Start of quoted field
            in_quotes = t
          )
        )

        ; Handle comma separator
        (equal(char ",")
          if(in_quotes then
            ; Comma inside quotes - add to current field
            current_field = strcat(current_field char)
          else
            ; Field separator - save current field and start new one
            fields = append(fields list(trim_string(current_field)))
            current_field = ""
          )
        )

        ; Regular character
        (t
          current_field = strcat(current_field char)
        )
      )
    )

    ; Add the last field
    fields = append(fields list(trim_string(current_field)))

    return(fields)
  )
)

; Function to add a CSV row to the table
procedure(add_csv_row(csv_table row_data headers row_index)
  prog((row_table col_count i)
    ; Create a table for this row
    row_table = makeTable(sprintf(nil "row_%d" (row_index + 1)) nil)

    ; Get column count (use minimum of headers and data)
    col_count = min(length(headers) length(row_data))

    ; Add each column value to the row table
    for(i 1 col_count
      header = nthelem(i headers)
      value = nthelem(i row_data)
      row_table[header] = value
    )

    ; Add row to main CSV table
    if(csv_table["rows"] then
      csv_table["rows"] = append(csv_table["rows"] list(row_table))
    else
      csv_table["rows"] = list(row_table)
    )

    printf("  Row %d: %d columns processed\n" (row_index + 1) col_count)
    return(t)
  )
)

; Function to get data from CSV table by row and column
procedure(get_csv_value(csv_table row_index column_name)
  prog((rows row_table)
    ; Check if table and rows exist
    unless(csv_table && csv_table["rows"]
      return(nil)
    )

    rows = csv_table["rows"]

    ; Check row index bounds
    if(row_index < 1 || row_index > length(rows) then
      return(nil)
    )

    ; Get the specific row
    row_table = nthelem(row_index rows)

    ; Return the column value
    return(row_table[column_name])
  )
)

; Function to get all values from a specific column
procedure(get_csv_column(csv_table column_name)
  prog((rows column_values)
    ; Check if table and rows exist
    unless(csv_table && csv_table["rows"]
      return(nil)
    )

    rows = csv_table["rows"]
    column_values = nil

    ; Extract column values from each row
    foreach(row_table rows
      column_values = append(column_values list(row_table[column_name]))
    )

    return(column_values)
  )
)

; Function to trim whitespace from string
procedure(trim_string(str)
  prog((result)
    ; Remove leading and trailing whitespace
    result = str
    
    ; Remove leading whitespace
    while(rexMatchp("^[ \t\n\r]" result)
      result = substring(result 2 strlen(result))
    )
    
    ; Remove trailing whitespace
    while(rexMatchp "[ \t\n\r]$" result)
      result = substring(result 1 (strlen(result) - 1))
    )
    
    return(result)
  )
)

; Function to display CSV table contents
procedure(display_csv_table(csv_table)
  prog((headers rows row_count col_count)
    printf("\nCSV Data Table Contents:\n")
    printf("========================\n")

    ; Get basic info
    headers = csv_table["headers"]
    rows = csv_table["rows"]
    row_count = csv_table["row_count"]
    col_count = csv_table["column_count"]

    ; Display summary
    printf("Columns: %d, Rows: %d\n\n" col_count row_count)

    ; Display headers
    printf("Headers: ")
    foreach(header headers
      printf("%s  " header)
    )
    printf("\n")
    printf("%s\n" (make_separator(col_count * 15)))

    ; Display first few rows (limit to 10 for readability)
    for(i 1 min(row_count 10)
      row_table = nthelem(i rows)
      printf("Row %2d: " i)
      foreach(header headers
        value = row_table[header]
        printf("%-12s  " (if value then value else ""))
      )
      printf("\n")
    )

    ; Show if there are more rows
    when(row_count > 10
      printf("... (%d more rows)\n" (row_count - 10))
    )
  )
)

; Helper function to check if file exists (reused from existing code pattern)
procedure(isFile(filename)
  prog((file_handle)
    file_handle = infile(filename)
    if(file_handle then
      close(file_handle)
      return(t)
    else
      return(nil)
    )
  )
)

; Function to get CSV table without display (for programmatic use)
procedure(get_csv_table(filename)
  return(load_csv_file(filename))
)

; Function to check if CSV file exists
procedure(csv_file_exists(filename)
  return(isFile(filename))
)

; Load message
println("Excel CSV File Reader Script loaded.")
println("Commands available:")
println("  read_csv_to_table filename     - Read CSV file into table with display")
println("  get_csv_table filename         - Get CSV table without display (programmatic use)")
println("  csv_file_exists filename       - Check if CSV file exists")
println("  get_csv_value table row col    - Get specific cell value")
println("  get_csv_column table col       - Get all values from a column")
println("")
println("Usage examples:")
println("  read_csv_to_table \"data.csv\"         - Read and display CSV file")
println("  table = get_csv_table \"report.csv\"   - Get CSV table programmatically")
println("  value = get_csv_value table 1 \"Name\" - Get value from row 1, Name column")
println("  names = get_csv_column table \"Name\"  - Get all values from Name column")
println("")
println("CSV file format support:")
println("  - First row treated as headers")
println("  - Comma-separated values")
println("  - Quoted fields with embedded commas")
println("  - Escaped quotes (double quotes)")
println("  - Empty fields supported")
println("")
println("Table structure:")
println("  table[\"headers\"] = list of column names")
println("  table[\"rows\"] = list of row tables")
println("  table[\"row_count\"] = number of data rows")
println("  table[\"column_count\"] = number of columns")
