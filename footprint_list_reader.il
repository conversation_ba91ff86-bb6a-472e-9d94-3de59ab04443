;; =========================================================================
;; Footprint List Reader Script for Cadence Allegro
;; - Checks current directory for "footprint_list_file"
;; - If exists, reads the file into a table variable
;; - Returns the table if file exists, nil if it doesn't
;; - Handles various file formats and error conditions
;; =========================================================================

axlCmdRegister("read_footprint_list" 'read_footprint_list)

; Main function to check for and read footprint list file
procedure(read_footprint_list()
  prog((filename footprint_table)
    ; Set the filename to look for
    filename = "footprint_list_file"
    
    ; Display what we're doing
    printf("Checking for file: %s\n" filename)
    
    ; Check if file exists and read it
    footprint_table = load_footprint_list_file(filename)
    
    ; Display results
    if(footprint_table then
      printf("Successfully loaded footprint list from: %s\n" filename)
      display_footprint_table(footprint_table)
    else
      printf("File not found: %s\n" filename)
    )
    
    ; Return the table (or nil)
    return(footprint_table)
  )
)

; Core function to load footprint list file into table
procedure(load_footprint_list_file(filename)
  prog((file_handle line footprint_table line_count entry_count)
    ; Check if file exists
    unless(isFile(filename)
      printf("File does not exist: %s\n" filename)
      return(nil)
    )
    
    ; Try to open the file
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open file: %s\n" filename)
      return(nil)
    )
    
    ; Create table to store footprint data
    footprint_table = makeTable("footprint_list" nil)
    
    ; Initialize counters
    line_count = 0
    entry_count = 0
    
    ; Read file line by line
    printf("Reading file: %s\n" filename)
    while(gets(line file_handle)
      line_count = line_count + 1
      
      ; Remove trailing newline and whitespace
      line = trim_string(line)
      
      ; Skip empty lines and comments (lines starting with #, ;, or //)
      unless(is_empty_or_comment(line)
        ; Process the line and add to table
        if(add_footprint_entry(footprint_table line entry_count) then
          entry_count = entry_count + 1
        )
      )
    )
    
    ; Close the file
    close(file_handle)
    
    ; Report results
    printf("File processing complete:\n")
    printf("  Lines read: %d\n" line_count)
    printf("  Entries added: %d\n" entry_count)
    
    ; Return the table if we have entries, nil otherwise
    if(entry_count > 0 then
      return(footprint_table)
    else
      printf("No valid entries found in file\n")
      return(nil)
    )
  )
)

; Function to add a footprint entry to the table
procedure(add_footprint_entry(table line entry_count)
  prog((parts key value)
    ; Check for key-value pair format (key=value or key:value)
    if(rexMatchp("=" line) then
      ; Split on equals sign
      parts = parseString(line "=")
      key = trim_string(car(parts))
      value = trim_string(cadr(parts))
    else if(rexMatchp(":" line) then
      ; Split on colon
      parts = parseString(line ":")
      key = trim_string(car(parts))
      value = trim_string(cadr(parts))
    else
      ; Use line as both key and value, with entry number as key
      key = sprintf(nil "entry_%d" (entry_count + 1))
      value = line
    )
    
    ; Add to table if we have valid key and value
    if(key && value && strlen(key) > 0 && strlen(value) > 0 then
      table[key] = value
      printf("  Added: %s -> %s\n" key value)
      return(t)
    else
      printf("  Skipped invalid line: %s\n" line)
      return(nil)
    )
  )
)

; Function to check if a line is empty or a comment
procedure(is_empty_or_comment(line)
  prog((trimmed_line)
    ; Get trimmed version
    trimmed_line = trim_string(line)
    
    ; Check if empty
    if(strlen(trimmed_line) == 0 then
      return(t)
    )
    
    ; Check if comment (starts with #, ;, or //)
    if(rexMatchp("^#" trimmed_line) then
      return(t)
    )
    if(rexMatchp("^;" trimmed_line) then
      return(t)
    )
    if(rexMatchp("^//" trimmed_line) then
      return(t)
    )
    
    return(nil)
  )
)

; Function to trim whitespace from string
procedure(trim_string(str)
  prog((result)
    ; Remove leading and trailing whitespace
    result = str
    
    ; Remove leading whitespace
    while(rexMatchp("^[ \t\n\r]" result)
      result = substring(result 2 strlen(result))
    )
    
    ; Remove trailing whitespace
    while(rexMatchp "[ \t\n\r]$" result)
      result = substring(result 1 (strlen(result) - 1))
    )
    
    return(result)
  )
)

; Function to display table contents
procedure(display_footprint_table(table)
  prog((keys)
    printf("\nFootprint List Table Contents:\n")
    printf("==============================\n")
    
    ; Get and sort keys
    keys = sort(getkeys(table))
    
    ; Display each entry
    foreach(key keys
      printf("  %s -> %s\n" key table[key])
    )
    
    printf("\nTotal entries: %d\n" length(keys))
  )
)

; Helper function to check if file exists (reused from existing code pattern)
procedure(isFile(filename)
  prog((file_handle)
    file_handle = infile(filename)
    if(file_handle then
      close(file_handle)
      return(t)
    else
      return(nil)
    )
  )
)

; Function to get just the table without display (for programmatic use)
procedure(get_footprint_list_table()
  prog((filename)
    filename = "footprint_list_file"
    return(load_footprint_list_file(filename))
  )
)

; Function to check if footprint list file exists
procedure(footprint_list_file_exists()
  return(isFile("footprint_list_file"))
)

; Load message
println("Footprint List Reader Script loaded.")
println("Commands available:")
println("  read_footprint_list        - Check for and read footprint_list_file")
println("  get_footprint_list_table   - Get table without display")
println("  footprint_list_file_exists - Check if file exists")
