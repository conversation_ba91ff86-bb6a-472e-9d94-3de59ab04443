;; =========================================================================
;; PCB Symbol Report Script for Cadence Allegro
;; - Analyzes all symbols in the current PCB design
;; - Extracts component properties and symbol information
;; - Creates a comprehensive spreadsheet with symbol values
;; - Includes reference designator, symbol name, value, tolerance, etc.
;; =========================================================================

axlCmdRegister("create_pcb_symbol_report" 'create_pcb_symbol_report)

procedure(create_pcb_symbol_report()
  prog((output_file include_props group_by_symbol)
    ; Check if we're in PCB Editor mode
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode with a board loaded.")
      return(nil)
    )
    
    ; Use simpler UI methods instead of forms
    output_file = axlUIPrompt("Enter output file name" "pcb_symbol_report.csv")
    
    ; If user canceled, return
    unless(output_file
      return(nil)
    )
    
    ; Ask about including all properties
    include_props = axlUIYesNo("Include all component properties?")
    
    ; Ask about grouping by symbol
    group_by_symbol = axlUIYesNo("Group components by symbol name?")
    
    ; Generate the report
    generate_pcb_report(output_file include_props group_by_symbol)
    
    return(t)
  )
)

procedure(generate_pcb_report(output_file include_props group_by_symbol)
  prog((components symbol_data file_handle common_props)
    ; Get all components in the design
    components = axlDBGetDesign()->components
    
    ; Check if any components were found
    unless(components
      axlUIConfirm("No components found in the current design.")
      return(nil)
    )
    
    ; Determine common properties to include in report
    common_props = list("PART_NUMBER", "VALUE", "TOLERANCE", "PACKAGE", "MANUFACTURER")
    
    ; Create a table to store symbol data
    symbol_data = makeTable("symbol_data" nil)
    
    ; Process each component
    foreach(component components
      ; Skip components without symbols
      when(component->symbol
        ; Get basic component info
        refdes = component->name
        symbol_name = component->symbol
        
        ; Get component properties
        props = makeTable("props" nil)
        foreach(prop_name common_props
          props[prop_name] = get_component_property(component prop_name)
        )
        
        ; Include all properties if requested
        when(include_props
          all_props = axlDBGetProperties(component)
          foreach(prop all_props
            prop_name = car(prop)
            prop_value = cdr(prop)
            unless(member(prop_name common_props)
              props[prop_name] = prop_value
            )
          )
        )
        
        ; Store data by symbol or by component
        if(group_by_symbol then
          ; Group by symbol name
          unless(symbol_data[symbol_name]
            symbol_data[symbol_name] = list()
          )
          symbol_data[symbol_name] = cons(list(refdes props) symbol_data[symbol_name])
        else
          ; Store by refdes
          symbol_data[refdes] = list(symbol_name props)
        )
      )
    )
    
    ; Create output file
    file_handle = outfile(output_file "w")
    
    ; Write headers
    if(group_by_symbol then
      ; Headers for grouped report
      fprintf(file_handle "Symbol Name,Reference Designators,Count")
      foreach(prop_name common_props
        fprintf(file_handle ",%s" prop_name)
      )
      when(include_props
        fprintf(file_handle ",Additional Properties")
      )
      fprintf(file_handle "\n")
      
      ; Write data grouped by symbol
      foreach(symbol_name getkeys(symbol_data)
        components_list = symbol_data[symbol_name]
        count = length(components_list)
        
        ; Get refdes list
        refdes_list = ""
        foreach(comp components_list
          refdes = car(comp)
          refdes_list = strcat(refdes_list " " refdes)
        )
        
        ; Get properties from first component
        first_comp = car(components_list)
        props = cadr(first_comp)
        
        ; Write basic info
        fprintf(file_handle "%s,%s,%d" 
                symbol_name refdes_list count)
        
        ; Write common properties
        foreach(prop_name common_props
          fprintf(file_handle ",%s" props[prop_name])
        )
        
        ; Write additional properties if requested
        when(include_props
          additional_props = ""
          foreach(prop_name getkeys(props)
            unless(member(prop_name common_props)
              additional_props = strcat(additional_props prop_name "=" props[prop_name] "; ")
            )
          )
          fprintf(file_handle ",%s" additional_props)
        )
        
        fprintf(file_handle "\n")
      )
    else
      ; Headers for component-by-component report
      fprintf(file_handle "Reference Designator,Symbol Name")
      foreach(prop_name common_props
        fprintf(file_handle ",%s" prop_name)
      )
      when(include_props
        fprintf(file_handle ",Additional Properties")
      )
      fprintf(file_handle "\n")
      
      ; Write data for each component
      foreach(refdes sort(getkeys(symbol_data))
        data = symbol_data[refdes]
        symbol_name = car(data)
        props = cadr(data)
        
        ; Write basic info
        fprintf(file_handle "%s,%s" refdes symbol_name)
        
        ; Write common properties
        foreach(prop_name common_props
          fprintf(file_handle ",%s" props[prop_name])
        )
        
        ; Write additional properties if requested
        when(include_props
          additional_props = ""
          foreach(prop_name getkeys(props)
            unless(member(prop_name common_props)
              additional_props = strcat(additional_props prop_name "=" props[prop_name] "; ")
            )
          )
          fprintf(file_handle ",%s" additional_props)
        )
        
        fprintf(file_handle "\n")
      )
    )
    
    ; Close output file
    close(file_handle)
    
    ; Report completion
    axlUIConfirm(sprintf(nil "PCB symbol report completed.\nReport saved to: %s" output_file))
    
    return(t)
  )
)

procedure(get_component_property(component prop_name)
  prog((value)
    value = component->prop[prop_name]
    if(value == nil then
      return("")
    else
      return(value)
    )
  )
)

; Load message
println("PCB Symbol Report Script loaded. Run 'create_pcb_symbol_report' to use.")
















