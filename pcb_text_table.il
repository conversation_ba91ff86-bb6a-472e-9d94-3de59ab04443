;; =========================================================================
;; PCB Text Table Script for Cadence Allegro
;; - Creates a text variable with 100 text entries including 40 duplicates
;; - Creates a table with "pcbxx" keys and unique text values
;; - Demonstrates duplicate filtering and table creation
;; =========================================================================

axlCmdRegister("create_pcb_text_table" 'create_pcb_text_table)

procedure(create_pcb_text_table()
  prog((text_variable unique_table unique_items pcb_counter original_count unique_count duplicate_count)
    ; Initialize variables
    text_variable = nil
    duplicate_count = 0
    pcb_counter = 1
    
    
        

   
    
    ; Process each text entry and add unique ones to the table
    println("\nProcessing entries and creating table with unique values:")
    println("========================================================")
    
    foreach(entry text_variable
      ; Check if this entry already exists as a value in the table
      if(member(entry table_values(unique_table)) then
        ; Entry already exists as a value, count as duplicate
        duplicate_count = duplicate_count + 1
        printf("Duplicate found: %s (skipping)\n" entry)
      else
        ; Entry doesn't exist, add it to the table with pcbxx key
        pcb_key = sprintf(nil "pcb%02d" pcb_counter)
        unique_table[pcb_key] = entry
        printf("Adding to table: %s -> %s\n" pcb_key entry)
        pcb_counter = pcb_counter + 1
      )
    )
    
    ; Get the count of unique items
    unique_count = length(getkeys(unique_table))
    
    ; Display summary
    println("\nSummary:")
    println("========")
    printf("Original text variable: %d entries\n" original_count)
    printf("Unique entries in table: %d\n" unique_count)
    printf("Duplicates filtered out: %d\n" duplicate_count)
    
    ; Display the final table contents
    println("\nFinal PCB Text Table (Key -> Value):")
    println("===================================")
    
    ; Get all keys and sort them for ordered display
    table_keys = sort(getkeys(unique_table))
    
    ; Display each key-value pair
    foreach(key table_keys
      printf("  %s -> %s\n" key unique_table[key])
    )
    
    ; Return the table for further use
    return(unique_table)
  )
)

; Helper function to shuffle a list using Fisher-Yates algorithm
procedure(shuffle_list(lst)
  prog((result temp_list len idx)
    result = nil
    
    ; Copy the original list
    temp_list = append(nil lst)
    
    ; Shuffle using Fisher-Yates algorithm
    while(temp_list
      ; Pick a random element from the remaining list
      idx = 1 + random(length(temp_list))
      
      ; Add it to the result
      result = cons(nthelem(idx temp_list) result)
      
      ; Remove it from the temp list
      temp_list = append(firstn(idx-1 temp_list) nthelem(idx+1 temp_list))
    )
    
    return(result)
  )
)

; Helper function to get all values from a table
procedure(table_values(tbl)
  prog((values)
    values = nil
    foreach(key getkeys(tbl)
      values = cons(tbl[key] values)
    )
    return(values)
  )
)

; Load message
println("PCB Text Table Script loaded. Run 'create_pcb_text_table' to execute.")
