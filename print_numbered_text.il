;; =========================================================================
;; Print Numbered Text Script for Cadence Allegro
;; - Creates a list containing text items numbered 1 through 100
;; - Prints each item in the list
;; - Demonstrates list creation and iteration
;; =========================================================================

axlCmdRegister("print_numbered_text" 'print_numbered_text)

procedure(print_numbered_text()
  prog((numbered_text_list)
    ; Create a list to hold the text items
    numbered_text_list = nil
    
    ; Populate the list with text items numbered 1 through 100
    ; Note: We build the list in reverse order (100 to 1) and then reverse it
    ; This is more efficient than using append() in a loop
    for(i 100 1 -1
      ; Create a text item with the format "Text #"
      text_item = sprintf(nil "Text %d" i)
      
      ; Add the item to the beginning of the list
      numbered_text_list = cons(text_item numbered_text_list)
    )
    
    ; Display header
    println("Numbered Text Items (1-100):")
    println("===========================")
    
    ; Print each item in the list
    ; We'll also show the index for clarity
    for(i 1 length(numbered_text_list)
      ; Get the current item
      current_item = nth(i-1 numbered_text_list)
      
      ; Print the item with its index
      printf("%3d: %s\n" i current_item)
    )
    
    ; Print summary
    println("\nSummary:")
    printf("Created a list with %d text items\n" length(numbered_text_list))
    
    ; Return the list for further use
    return(numbered_text_list)
  )
)

; Alternative implementation using a different approach
procedure(print_numbered_text_alt()
  prog((numbered_text_list)
    ; Create an empty list
    numbered_text_list = makeTable("text_items")
    
    ; Populate the table with text items numbered 1 through 100
    for(i 1 100
      ; Create a text item with the format "Text #"
      text_item = sprintf(nil "Text %d" i)
      
      ; Add the item to the table using the index as key
      numbered_text_list[i] = text_item
    )
    
    ; Display header
    println("Numbered Text Items (1-100) - Alternative Method:")
    println("================================================")
    
    ; Print each item in the table
    for(i 1 100
      ; Get the current item
      current_item = numbered_text_list[i]
      
      ; Print the item with its index
      printf("%3d: %s\n" i current_item)
    )
    
    ; Return the table for further use
    return(numbered_text_list)
  )
)

; Load message
println("Print Numbered Text Script loaded. Run 'print_numbered_text' to use.")