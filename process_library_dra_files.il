;; =========================================================================
;; Process Library DRA Files Script for Cadence Allegro
;; - Loops through all .dra files in specified library directory
;; - Opens each file in Allegro
;; - Runs the FLIR library update script on each file
;; - Saves and closes each file before moving to the next
;; =========================================================================

axlCmdRegister("process_library_dra_files" 'process_library_dra_files)

procedure(process_library_dra_files()
  prog((form_id)
    ; Create form to get library directory
    form_id = axlFormCreate(
      (list
        (list "text" "Library Directory:")
        (list "field" "lib_dir" 40 "./library")
        (list "text" "FLIR Update Script Path:")
        (list "field" "script_path" 40 "E:/pads/Allegro/Skill/ai/FLIR_SYMBOL_UPDATE.il")
      )
      "Process Library DRA Files" 
      (list "OK" "Cancel")
      'process_library_form_callback
      t
    )
    
    ; Display the form
    axlFormDisplay(form_id)
    
    return(t)
  )
)

procedure(process_library_form_callback(form)
  prog((lib_dir script_path)
    if(form->curField == "OK" then
      ; Get input values
      lib_dir = form->lib_dir
      script_path = form->script_path
      
      ; Close form
      axlFormClose(form)
      
      ; Process the library
      process_library(lib_dir script_path)
    else
      ; Cancel was clicked
      axlFormClose(form)
    )
    
    return(t)
  )
)

procedure(process_library(lib_dir script_path)
  prog((dra_files total_count current_count status_form)
    ; Check if directory exists
    unless(isDir(lib_dir)
      axlUIConfirm(sprintf(nil "Directory not found: %s" lib_dir))
      return(nil)
    )
    
    ; Get all .dra files in the directory
    dra_files = getDraFiles(lib_dir)
    total_count = length(dra_files)
    
    ; Check if any .dra files were found
    unless(total_count
      axlUIConfirm(sprintf(nil "No .dra files found in %s" lib_dir))
      return(nil)
    )
    
    ; Confirm operation with user
    unless(axlUIYesNo(sprintf(nil "Found %d .dra files. Process all files?" total_count))
      return(nil)
    )
    
    ; Create status form
    status_form = axlFormCreate(
      (list
        (list "text" "Processing DRA Files...")
        (list "field" "current_file" 40)
        (list "text" "Progress:")
        (list "field" "progress" 40)
      )
      "Processing Status" 
      nil
      nil
      t
    )
    axlFormDisplay(status_form)
    
    ; Process each .dra file
    current_count = 0
    foreach(dra_file dra_files
      current_count = current_count + 1
      
      ; Update status form
      axlFormSetField(status_form "current_file" dra_file)
      axlFormSetField(status_form "progress" sprintf(nil "%d of %d (%d%%)" 
                                                   current_count total_count
                                                   (current_count * 100 / total_count)))
      
      ; Process the file
      process_dra_file(dra_file script_path)
    )
    
    ; Close status form
    axlFormClose(status_form)
    
    ; Report completion
    axlUIConfirm(sprintf(nil "Completed processing %d .dra files" total_count))
    
    return(t)
  )
)

procedure(process_dra_file(dra_file script_path)
  prog((result)
    ; Display current file
    println(sprintf(nil "Processing: %s" dra_file))
    
    ; Close current design if open
    when(axlIsDesignOpen()
      axlCloseDesign()
    )
    
    ; Open the .dra file
    result = axlOpenDesign(dra_file)
    unless(result
      println(sprintf(nil "Failed to open: %s" dra_file))
      return(nil)
    )
    
    ; Load and run the FLIR update script
    println(sprintf(nil "Loading script: %s" script_path))
    load(script_path)
    
    ; Run the main routine
    println("Running Footprint_Main_Routine")
    Footprint_Main_Routine()
    
    ; Save the design
    axlSaveDesign()
    
    ; Close the design
    axlCloseDesign()
    
    return(t)
  )
)

procedure(getDraFiles(dir)
  prog((all_files dra_files)
    ; Get all files in directory
    all_files = getDirFiles(dir)
    
    ; Filter for .dra files
    dra_files = nil
    foreach(file all_files
      when(stringEndsWith(lowerCase(file) ".dra")
        dra_files = cons(strcat(dir "/" file), dra_files)
      )
    )
    
    return(reverse(dra_files))
  )
)

; Helper function to check if string ends with suffix
procedure(stringEndsWith(str suffix)
  prog((str_len suffix_len)
    str_len = strlen(str)
    suffix_len = strlen(suffix)
    
    if(str_len < suffix_len then
      return(nil)
    else
      return(equal(substring(str (str_len - suffix_len + 1) str_len) suffix))
    )
  )
)

; Helper function to check if directory exists
procedure(isDir(dir)
  prog((result)
    result = nil
    
    ; Try to open directory
    if(isFile(dir) then
      ; It's a file, not a directory
      result = nil
    else
      ; Check if directory exists by trying to get files
      result = (getDirFiles(dir) != nil)
    )
    
    return(result)
  )
)

; Load message
println("Process Library DRA Files Script loaded. Run 'process_library_dra_files' to use.")