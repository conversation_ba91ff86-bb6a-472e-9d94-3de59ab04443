;; =========================================================================
;; Save PCB Symbols to Library Script for Cadence Allegro
;; - Extracts all symbols from current PCB into "original_library" directory
;; - Creates directory if it doesn't exist
;; - Handles duplicate symbols
;; - Provides progress feedback during operation
;; =========================================================================
;
; load "e:/pads/allegro/skill/ai/save_symbols_to_library.il"
;
axlCmdRegister("save_pcb_symbols_to_library" 'save_pcb_symbols_to_library)

;******************************************************
;
; Main Routine
;
;******************************************************


procedure(save_pcb_symbols_to_library()

  prog((target_dir footprint_names unique_symbols total_count success_count duplicate_count)
 
 
     duplicate_count = 0
     footprint_key = 1
     unique_footprints = nil


;
; Check if a design is currently open if not exit program
;
      unless(axlIsDesignOpen()
        axlUIConfirm("No design is currently open.")
        println("No design is currently open.")
        return(nil)
    )
  
   ;
  ; Check if we're in the PCB Editor(not in symbol editor)
  ;
 
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    ); End unless
    
    ;
    ; Set target directory for saving symbols
    ;
    
    target_dir = "./original_library"
    
   ;
   ; Get PCB file name
   ;
   
   Pcb_File_Name = axlCurrent_Design()
   
   ;
   ; Get the design type
   ;
       design_type = axlDesignType(t)
      
   ;
   ; Get the working directory
   ;
       working_dir = axlGetVariable("WORKINGDIR")
       
       ; Construct full path
    full_path = strcat(working_dir "/" design_name)
   
   
   ;
   ; If Directory does not exist create it and output message.
   ;
   
    if(createDir(target_dir) 
     then
      axlUIConfirm(sprintf(nil "Creating directory for symbols :  %s" target_dir))
      
     else
;     
; if list_footprints_File exists then get file into a variable     
; 


	unique_footprints

    ); End If 
 
 
    ;
    ; Get all footprint names in the design. 
    ;
  
    footprint_names = axlDBGetDesign()->symbols~>name
   
foreach(entry footprint_names

   ;
   ;Create a table to track unique symbols
   ; 
    
    unique_symbols = makeTable("unique_symbols" nil)
    
    ;
    ; Identify unique symbols
    ;
    
    
 foreach(entry footprint_names
 
 ;
 ; Check if this entry already exists as a value in the table
 ;
 
   if(member(entry footprint_names(unique _symbols) then
 
 :
 ; Entry already exists as a value, count as duplicate. Delete and add new one
 ;
 	duplicate_count = duplicate_count + 1
        printf("Duplicate found: %s (skipping)\n" entry)
        
 ;
 ;	delete and then add new record
 ;
   else 

; 
; Entry doesn't exist, add it to the table with pcbxx key
;
        footprint_key = sprintf(nil "footprint %02d" footprint_counter)
        unique_table[footprint_key] = footprint_namnes
        printf("Adding to table: %s -> %s\n" footprint_key Pcb_File_Name footprint_names)
        footprint_counter = footprint_counter + 1   
    
    
    
    
    
    
    
    
    
    
    
    
    foreach(footprint footprint_names
     
      unique_symbols[footprint] = footprint
    )  
  
  break()
  
    
    ; Get total count of unique symbols
    total_count = length(unique_symbols)
  
    ; Confirm operation with user
    unless(axlUIYesNo(sprintf(nil "Found %d unique symbols. Extract all to %s directory?" 
                             total_count target_dir))
      return(nil)
    )
    
    ; Process each unique symbol
    success_count = 0
    foreach(symbol_name unique_symbols
      component = unique_symbols[symbol_name]
      when(extract_symbol_to_library(component target_dir)
        success_count = success_count + 1
      )
      
      ; Update progress every 5 symbols
      when(remainder(success_count 5) == 0
        axlUIConfirm(sprintf(nil "Progress: %d of %d symbols processed" 
                            success_count total_count) nil)
      )
    )
    
    ; Report final results
    axlUIConfirm(sprintf(nil "Completed: %d of %d unique symbols saved to %s" 
                        success_count total_count target_dir))
    
    return(t)
  )
)

;******************************************************
;
; extract_symbol_to_library
;
;******************************************************


procedure(extract_symbol_to_library(component target_dir)
  prog((symbol_name symbol_path result)
    ;
    ; Get symbol name from component
    ;
    symbol_name = component->symbol~>name
    
    ; Skip if no symbol
    unless(symbol_name
      return(nil)
    )
    
    ; Create target path
    symbol_path = strcat(target_dir "/" symbol_name)
    
    ; Show progress
    println(sprintf(nil "Extracting symbol: %s" symbol_name))
    
    ; Extract the symbol
    result = nil
    
    ; Use axlExtractToFile to extract the symbol from the component
    result = axlExtractToFile(
      component,
      symbol_path,
      "SYMBOL"  ; Extract as symbol
    )
    
    ; Return result
    return(result)
  )
)






; Load message
println("Save PCB Symbols to Library Script loaded. Run 'save_pcb_symbols_to_library' to use.")