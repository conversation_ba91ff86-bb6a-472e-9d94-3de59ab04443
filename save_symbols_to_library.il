;; =========================================================================
;; Save PCB Symbols to Library Script for Cadence Allegro
;; - Extracts all symbols from current PCB into "original_library" directory
;; - Creates directory if it doesn't exist
;; - Handles duplicate symbols
;; - Provides progress feedback during operation
;;
;; - We keep track of the PCB name and footprint name so we know which PCB file we 
;; - saved the footprint from. Either the variable Unique_Footprints is setto nil or 
;; - reads the csv file
;; - 
;; -  The format for the file name is :
;; -  1 = Key - a number 
;; -  2 = PCB FileName 
;; -  4 = footprint_name 
;; -  5 = Place_Bound_Shape - does the shape exist
;; -  6 = Max_Height - value or nil if it does not exist
;; -  7 = DFA_Bound_Shape - does the shape exist
;; -  8 = DISPLAY_TOP - did we create the shape on the display top layer. 
;; =========================================================================
;
; load "e:/pads/allegro/skill/ai/save_symbols_to_library.il"
;
axlCmdRegister("save_pcb_symbols_to_library" 'save_pcb_symbols_to_library)

;******************************************************
;
; Main Routine
;
;******************************************************


procedure(save_pcb_symbols_to_library()

  prog((target_dir footprint_names unique_symbols total_count success_count duplicate_count)
 
 
     duplicate_count = 0
     footprint_key = 1
     unique_footprints = nil


;
; Check if a design is currently open if not exit program
;
    unless(axlIsDesignOpen()
        axlUIConfirm("No design is currently open.")
        println("No design is currently open.")
        return(nil)
    );End Unless
  
   ;
  ; Check if we're in the PCB Editor(not in symbol editor)
  ;
 
    unless(axlDesignType(t) == "BOARD"
      axlUIConfirm("This script must be run in PCB Editor mode, not Symbol Editor.")
      return(nil)
    ); End unless
    
    ;
    ; Set target directory for saving symbols
    ;
    
    target_dir = "./original_library"
    
   ;
   ; Get PCB file name
   ;
   
   Pcb_File_Name = axlCurrent_Design()
   
   ;
   ; Get the design type
   ;
       design_type = axlDesignType(t)
      
   ;
   ; Get the working directory
   ;
       working_dir = axlGetVariable("WORKINGDIR")
       
       ; Construct full path
    full_path = strcat(working_dir "/" design_name)
   
   
   ;
   ; If Directory does not exist create it and output message.
   ;
   
    if(createDir(target_dir) 
     then
      axlUIConfirm(sprintf(nil "Creating directory for symbols :  %s" target_dir))
      
     else
;     
; if Footprint_List_File exists then get file into a variable unique_footprints   
; 

    unique_footprints = read_csv_to_table(Footprint_file_list)
    ); End If 
 
 
    ;
    ; Get all footprint names in the design. 
    ;
  
    footprint_names = axlDBGetDesign()->symbols~>name
   
   
    
 foreach(entry footprint_names
 
 ;
 ; Check if this entry already exists as a value in the table
 ;
 
   if(member(entry footprint_names(unique_footprints) then
 
 ;
 ; Entry already exists as a value, count as duplicate. Delete and add new one
 ;
 	duplicate_count = duplicate_count + 1
        printf("Duplicate found: %s (skipping)\n" entry)
        
 ;
 ;	delete and then add new record
 ;
   else 

; 
; Entry doesn't exist, add it to the table with pcbxx key
;
        footprint_key = sprintf(nil "footprint %02d" footprint_counter)
        unique_table[footprint_key] = footprint_namnes
        printf("Adding to table: %s -> %s\n" footprint_key Pcb_File_Name footprint_names)
        footprint_counter = footprint_counter + 1   
    );End If
    ); End Foreach
    
    
 ;  
 ;  
 ;
    foreach(footprint footprint_names
     
      unique_symbols[footprint] = footprint
    )  
  
  break()
  
    
    ; Get total count of unique symbols
    total_count = length(unique_symbols)
  
    ; Confirm operation with user
    unless(axlUIYesNo(sprintf(nil "Found %d unique symbols. Extract all to %s directory?" 
                             total_count target_dir))
      return(nil)
    )
    
    ; Process each unique symbol
    success_count = 0
    foreach(symbol_name unique_symbols
      component = unique_symbols[symbol_name]
      when(extract_symbol_to_library(component target_dir)
        success_count = success_count + 1
      )
      
      ; Update progress every 5 symbols
      when(remainder(success_count 5) == 0
        axlUIConfirm(sprintf(nil "Progress: %d of %d symbols processed" 
                            success_count total_count) nil)
      )
    )
    
    ; Report final results
    axlUIConfirm(sprintf(nil "Completed: %d of %d unique symbols saved to %s" 
                        success_count total_count target_dir))
    
    return(t)
    
  ) End Prog
); End Procedure

;******************************************************
;
; extract_symbol_to_library
;
;******************************************************


procedure(extract_symbol_to_library(component target_dir)
  prog((symbol_name symbol_path result)
    ;
    ; Get symbol name from component
    ;
    symbol_name = component->symbol~>name
    
    ; Skip if no symbol
    unless(symbol_name
      return(nil)
    )
    
    ; Create target path
    symbol_path = strcat(target_dir "/" symbol_name)
    
    ; Show progress
    println(sprintf(nil "Extracting symbol: %s" symbol_name))
    
    ; Extract the symbol
    result = nil
    
    ; Use axlExtractToFile to extract the symbol from the component
    result = axlExtractToFile(
      component,
      symbol_path,
      "SYMBOL"  ; Extract as symbol
    )
    
    ; Return result
    return(result)
  )
)

;***********************************************************
;
; Read the passed file name that is a CSV file into a table if it exists
;
;**********************************************************

procedure(read_csv_to_table(filename)
  prog((csv_data)
    ; Display what we're doing
    printf("Reading CSV file: %s\n" filename)
;
; Check if file exists and read it
;
    csv_data = load_csv_file(filename)

    ; Display results
    if(csv_data then
      printf("Successfully loaded CSV data from: %s\n" filename)
      display_csv_table(csv_data)
    else
      printf("File not found or could not be read: %s\n" filename)
    )

    ; Return the table (or nil)
    return(csv_data)
  )
)

;***********************************************************
;
; Core function to load CSV file into table structure
;
;***********************************************************

procedure(load_csv_file(filename)
  prog((file_handle line csv_table headers row_data line_count row_count)
;    
; Check if file exists. If not return nil
;
    unless(axlFileExiasts(filename)
      printf("CSV file does not exist: %s\n" filename)
      return(nil)
    )
    
;   
; Try to open the file
;
    file_handle = infile(filename)
    unless(file_handle
      printf("Failed to open CSV file: %s\n" filename)
      return(nil)
    )
    
;  
; Create main table to store CSV data
;
    csv_table = makeTable("csv_data" nil)
    csv_table["headers"] = nil
    csv_table["rows"] = nil
    csv_table["row_count"] = 0
    csv_table["column_count"] = 0

    ; Initialize counters
    line_count = 0
    row_count = 0
    headers = nil

    ; Read file line by line
    printf("Processing CSV file: %s\n" filename)
    while(gets(line file_handle)
      line_count = line_count + 1

      ; Remove trailing newline
      line = trim_string(line)

      ; Skip empty lines
      unless(strlen(line) == 0
        ; Parse CSV line
        row_data = parse_csv_line(line)

        if(line_count == 1 then
          ; First line contains headers
          headers = row_data
          csv_table["headers"] = headers
          csv_table["column_count"] = length(headers)
          printf("Headers found: %d columns\n" length(headers))
          foreach(header headers
            printf("  Column: %s\n" header)
          )
        else
          ; Data rows
          if(add_csv_row(csv_table row_data headers row_count) then
            row_count = row_count + 1
          )
        )
      )
    )

    ; Close the file
    close(file_handle)

    ; Update row count
    csv_table["row_count"] = row_count

    ; Report results
    printf("CSV processing complete:\n")
    printf("  Total lines: %d\n" line_count)
    printf("  Header columns: %d\n" csv_table["column_count"])
    printf("  Data rows: %d\n" row_count)

    ; Return the table if we have data, nil otherwise
    if(row_count > 0 then
      return(csv_table)
    else
      printf("No valid data rows found in CSV file\n")
      return(nil)
    )
  )
)

; Function to parse a CSV line into fields
procedure(parse_csv_line(line)
  prog((fields current_field in_quotes char i)
    fields = nil
    current_field = ""
    in_quotes = nil

    ; Process each character in the line
    for(i 1 strlen(line)
      char = substring(line i i)

      cond(
        ; Handle quotes
        (equal(char "\"")
          if(in_quotes then
            ; Check for escaped quote (double quote)
            if(i < strlen(line) && equal(substring(line (i+1) (i+1)) "\"") then
              ; Escaped quote - add single quote to field
              current_field = strcat(current_field "\"")
              i = i + 1  ; Skip next quote
            else
              ; End of quoted field
              in_quotes = nil
            )
          else
            ; Start of quoted field
            in_quotes = t
          )
        )

        ; Handle comma separator
        (equal(char ",")
          if(in_quotes then
            ; Comma inside quotes - add to current field
            current_field = strcat(current_field char)
          else
            ; Field separator - save current field and start new one
            fields = append(fields list(trim_string(current_field)))
            current_field = ""
          )
        )

        ; Regular character
        (t
          current_field = strcat(current_field char)
        )
      )
    )

    ; Add the last field
    fields = append(fields list(trim_string(current_field)))

    return(fields)
  )
)

; Function to add a CSV row to the table
procedure(add_csv_row(csv_table row_data headers row_index)
  prog((row_table col_count i)
    ; Create a table for this row
    row_table = makeTable(sprintf(nil "row_%d" (row_index + 1)) nil)

    ; Get column count (use minimum of headers and data)
    col_count = min(length(headers) length(row_data))

    ; Add each column value to the row table
    for(i 1 col_count
      header = nthelem(i headers)
      value = nthelem(i row_data)
      row_table[header] = value
    )

    ; Add row to main CSV table
    if(csv_table["rows"] then
      csv_table["rows"] = append(csv_table["rows"] list(row_table))
    else
      csv_table["rows"] = list(row_table)
    )

    printf("  Row %d: %d columns processed\n" (row_index + 1) col_count)
    return(t)
  )
)

; Function to get data from CSV table by row and column
procedure(get_csv_value(csv_table row_index column_name)
  prog((rows row_table)
    ; Check if table and rows exist
    unless(csv_table && csv_table["rows"]
      return(nil)
    )

    rows = csv_table["rows"]

    ; Check row index bounds
    if(row_index < 1 || row_index > length(rows) then
      return(nil)
    )

    ; Get the specific row
    row_table = nthelem(row_index rows)

    ; Return the column value
    return(row_table[column_name])
  )
)

; Function to get all values from a specific column
procedure(get_csv_column(csv_table column_name)
  prog((rows column_values)
    ; Check if table and rows exist
    unless(csv_table && csv_table["rows"]
      return(nil)
    )

    rows = csv_table["rows"]
    column_values = nil

    ; Extract column values from each row
    foreach(row_table rows
      column_values = append(column_values list(row_table[column_name]))
    )

    return(column_values)
  )
)

; Function to trim whitespace from string
procedure(trim_string(str)
  prog((result)
    ; Remove leading and trailing whitespace
    result = str
    
    ; Remove leading whitespace
    while(rexMatchp("^[ \t\n\r]" result)
      result = substring(result 2 strlen(result))
    )
    
    ; Remove trailing whitespace
    while(rexMatchp "[ \t\n\r]$" result)
      result = substring(result 1 (strlen(result) - 1))
    )
    
    return(result)
  )
)

; Function to display CSV table contents
procedure(display_csv_table(csv_table)
  prog((headers rows row_count col_count)
    printf("\nCSV Data Table Contents:\n")
    printf("========================\n")

    ; Get basic info
    headers = csv_table["headers"]
    rows = csv_table["rows"]
    row_count = csv_table["row_count"]
    col_count = csv_table["column_count"]

    ; Display summary
    printf("Columns: %d, Rows: %d\n\n" col_count row_count)

    ; Display headers
    printf("Headers: ")
    foreach(header headers
      printf("%s  " header)
    )
    printf("\n")
    printf("%s\n" (make_separator(col_count * 15)))

    ; Display first few rows (limit to 10 for readability)
    for(i 1 min(row_count 10)
      row_table = nthelem(i rows)
      printf("Row %2d: " i)
      foreach(header headers
        value = row_table[header]
        printf("%-12s  " (if value then value else ""))
      )
      printf("\n")
    )

    ; Show if there are more rows
    when(row_count > 10
      printf("... (%d more rows)\n" (row_count - 10))
    )
  )
)

;
; Function to get CSV table without display (for programmatic use)
;
procedure(get_csv_table(filename)
  return(load_csv_file(filename))
)

; Function to check if CSV file exists
procedure(csv_file_exists(filename)
  return(isFile(filename))
)

; Load message
;println("Excel CSV File Reader Script loaded.")
;println("Commands available:")
;println("  read_csv_to_table filename     - Read CSV file into table with display")
;println("  get_csv_table filename         - Get CSV table without display (programmatic use)")
;println("  csv_file_exists filename       - Check if CSV file exists")
;println("  get_csv_value table row col    - Get specific cell value")
;println("  get_csv_column table col       - Get all values from a column")
;println("")
;println("Usage examples:")
;println("  read_csv_to_table \"data.csv\"         - Read and display CSV file")
;println("  table = get_csv_table \"report.csv\"   - Get CSV table programmatically")
;println("  value = get_csv_value table 1 \"Name\" - Get value from row 1, Name column")
;println("  names = get_csv_column table \"Name\"  - Get all values from Name column")
;println("")
;println("CSV file format support:")
;println("  - First row treated as headers")
;println("  - Comma-separated values")
;println("  - Quoted fields with embedded commas")
;println("  - Escaped quotes (double quotes)")
;println("  - Empty fields supported")
;println("")
;println("Table structure:")
;println("  table[\"headers\"] = list of column names")
;println("  table[\"rows\"] = list of row tables")
;println("  table[\"row_count\"] = number of data rows")
;println("  table[\"column_count\"] = number of columns")













; Load message
println("Save PCB Symbols to Library Script loaded. Run 'save_pcb_symbols_to_library' to use.")