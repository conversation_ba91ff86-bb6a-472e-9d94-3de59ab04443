;; =========================================================================
;; Table Update Manager Script for Cadence Allegro
;; - Creates a table with sample data
;; - Searches for entries by key or value
;; - Deletes found entries and adds new entries
;; - Demonstrates table manipulation operations
;; =========================================================================

axlCmdRegister("manage_table_entries" 'manage_table_entries)

procedure(manage_table_entries()
  prog((data_table search_key new_key new_value operation_type)
    ; Display header
    println("Table Update Manager:")
    println("====================")
    
    ; Create and populate initial table
    data_table = create_sample_table()
    
    ; Display initial table contents
    display_table_contents(data_table "Initial Table Contents")
    
    ; Get user input for operation
    search_key = axlUIPrompt("Enter key to search for (or 'demo' for automatic demo)" "demo")
    
    ; If user canceled, return
    unless(search_key
      return(nil)
    )
    
    ; Run demo or user-specified operation
    if(search_key == "demo" then
      run_demo_operations(data_table)
    else
      ; Get new entry details
      new_key = axlUIPrompt("Enter new key" search_key)
      new_value = axlUIPrompt("Enter new value" "Updated Value")
      
      ; Perform the update operation
      if(new_key && new_value then
        update_table_entry(data_table search_key new_key new_value)
        display_table_contents(data_table "Updated Table Contents")
      )
    )
    
    return(data_table)
  )
)

; Function to create a sample table with initial data
procedure(create_sample_table()
  prog((table)
    ; Create the table
    table = makeTable("sample_data" nil)
    
    ; Add sample entries
    table["key1"] = "Value One"
    table["key2"] = "Value Two"
    table["key3"] = "Value Three"
    table["item_a"] = "Data A"
    table["item_b"] = "Data B"
    table["config_x"] = "Setting X"
    table["config_y"] = "Setting Y"
    
    println("Created sample table with initial data")
    return(table)
  )
)

; Function to search for and update a table entry
procedure(update_table_entry(table search_key new_key new_value)
  prog((found old_value)
    println(sprintf(nil "\nSearching for key: %s" search_key))
    
    ; Check if the search key exists in the table
    if(table[search_key] then
      ; Key found - get the old value
      old_value = table[search_key]
      found = t
      
      printf("Found entry: %s -> %s\n" search_key old_value)
      
      ; Delete the old entry
      table[search_key] = nil
      printf("Deleted entry: %s\n" search_key)
      
      ; Add the new entry
      table[new_key] = new_value
      printf("Added new entry: %s -> %s\n" new_key new_value)
      
      ; Summary
      println("\nOperation Summary:")
      printf("  Deleted: %s -> %s\n" search_key old_value)
      printf("  Added:   %s -> %s\n" new_key new_value)
      
    else
      ; Key not found
      printf("Key '%s' not found in table\n" search_key)
      
      ; Ask if user wants to add it anyway
      if(axlUIYesNo(sprintf(nil "Key '%s' not found. Add new entry anyway?" search_key)) then
        table[new_key] = new_value
        printf("Added new entry: %s -> %s\n" new_key new_value)
        found = t
      else
        found = nil
      )
    )
    
    return(found)
  )
)

; Function to search for entries by value
procedure(find_key_by_value(table search_value)
  prog((found_keys)
    found_keys = nil
    
    ; Search through all table entries
    foreach(key getkeys(table)
      when(table[key] == search_value
        found_keys = cons(key found_keys)
      )
    )
    
    return(found_keys)
  )
)

; Function to display table contents
procedure(display_table_contents(table title)
  prog((keys)
    ; Display header
    println(sprintf(nil "\n%s:" title))
    println(sprintf(nil "%s" (make_separator(strlen(title) + 1))))
    
    ; Get and sort keys
    keys = sort(getkeys(table))
    
    ; Display each entry
    if(keys then
      foreach(key keys
        printf("  %s -> %s\n" key table[key])
      )
      printf("\nTotal entries: %d\n" length(keys))
    else
      println("  (Table is empty)")
    )
  )
)

; Function to run demonstration operations
procedure(run_demo_operations(table)
  prog()
    println("\nRunning Demo Operations:")
    println("========================")
    
    ; Demo 1: Update existing entry
    println("\nDemo 1: Update existing entry 'key2'")
    update_table_entry(table "key2" "key2_updated" "New Value Two")
    
    ; Demo 2: Replace entry with different key
    println("\nDemo 2: Replace 'item_a' with 'item_alpha'")
    update_table_entry(table "item_a" "item_alpha" "Alpha Data")
    
    ; Demo 3: Try to update non-existent entry
    println("\nDemo 3: Try to update non-existent entry 'missing_key'")
    update_table_entry(table "missing_key" "new_entry" "Brand New Value")
    
    ; Demo 4: Search by value
    println("\nDemo 4: Search for entries with value 'Setting X'")
    found_keys = find_key_by_value(table "Setting X")
    if(found_keys then
      printf("Found keys with value 'Setting X': %s\n" found_keys)
    else
      println("No keys found with value 'Setting X'")
    )
    
    ; Display final results
    display_table_contents(table "Final Demo Results")
  )
)

; Helper function to create separator line
procedure(make_separator(length)
  prog((separator i)
    separator = ""
    for(i 1 length
      separator = strcat(separator "=")
    )
    return(separator)
  )
)

; Function to clear all table entries
procedure(clear_table(table)
  prog((keys)
    keys = getkeys(table)
    foreach(key keys
      table[key] = nil
    )
    printf("Cleared %d entries from table\n" length(keys))
  )
)

; Function to copy table entries
procedure(copy_table_entry(source_table dest_table key)
  prog((value)
    if(source_table[key] then
      value = source_table[key]
      dest_table[key] = value
      printf("Copied entry: %s -> %s\n" key value)
      return(t)
    else
      printf("Key '%s' not found in source table\n" key)
      return(nil)
    )
  )
)

; Load message
println("Table Update Manager Script loaded.")
println("Commands available:")
println("  manage_table_entries - Interactive table management")
println("  Run 'manage_table_entries' and enter 'demo' for automatic demonstration")
